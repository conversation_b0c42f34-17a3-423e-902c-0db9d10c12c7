
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for modules/swap</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> modules/swap</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>316/548</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.16% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>110/296</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.7% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>22/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.64% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>313/543</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="swap.handlers.js"><a href="swap.handlers.js.html">swap.handlers.js</a></td>
	<td data-value="79.16" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 79%"></div><div class="cover-empty" style="width: 21%"></div></div>
	</td>
	<td data-value="79.16" class="pct medium">79.16%</td>
	<td data-value="96" class="abs medium">76/96</td>
	<td data-value="63.15" class="pct medium">63.15%</td>
	<td data-value="57" class="abs medium">36/57</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="5" class="abs high">4/5</td>
	<td data-value="79.16" class="pct medium">79.16%</td>
	<td data-value="96" class="abs medium">76/96</td>
	</tr>

<tr>
	<td class="file low" data-value="swap.resolvers.js"><a href="swap.resolvers.js.html">swap.resolvers.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file medium" data-value="swap.service.js"><a href="swap.service.js.html">swap.service.js</a></td>
	<td data-value="58.52" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 58%"></div><div class="cover-empty" style="width: 42%"></div></div>
	</td>
	<td data-value="58.52" class="pct medium">58.52%</td>
	<td data-value="393" class="abs medium">230/393</td>
	<td data-value="37.01" class="pct low">37.01%</td>
	<td data-value="181" class="abs low">67/181</td>
	<td data-value="76.19" class="pct medium">76.19%</td>
	<td data-value="21" class="abs medium">16/21</td>
	<td data-value="58.5" class="pct medium">58.5%</td>
	<td data-value="388" class="abs medium">227/388</td>
	</tr>

<tr>
	<td class="file low" data-value="swap.validation.js"><a href="swap.validation.js.html">swap.validation.js</a></td>
	<td data-value="17.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.85" class="pct low">17.85%</td>
	<td data-value="56" class="abs low">10/56</td>
	<td data-value="12.06" class="pct low">12.06%</td>
	<td data-value="58" class="abs low">7/58</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="8" class="abs low">2/8</td>
	<td data-value="17.85" class="pct low">17.85%</td>
	<td data-value="56" class="abs low">10/56</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-16T09:39:32.104Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    