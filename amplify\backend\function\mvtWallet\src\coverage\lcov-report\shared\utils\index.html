
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for shared/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> shared/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.86% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>181/324</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.3% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>163/352</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.22% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>30/49</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.86% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>181/324</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="handlerUtils.js"><a href="handlerUtils.js.html">handlerUtils.js</a></td>
	<td data-value="78.88" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.88" class="pct medium">78.88%</td>
	<td data-value="90" class="abs medium">71/90</td>
	<td data-value="58.62" class="pct medium">58.62%</td>
	<td data-value="87" class="abs medium">51/87</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="78.88" class="pct medium">78.88%</td>
	<td data-value="90" class="abs medium">71/90</td>
	</tr>

<tr>
	<td class="file medium" data-value="logger.js"><a href="logger.js.html">logger.js</a></td>
	<td data-value="67.74" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 67%"></div><div class="cover-empty" style="width: 33%"></div></div>
	</td>
	<td data-value="67.74" class="pct medium">67.74%</td>
	<td data-value="31" class="abs medium">21/31</td>
	<td data-value="38.63" class="pct low">38.63%</td>
	<td data-value="44" class="abs low">17/44</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="12" class="abs medium">9/12</td>
	<td data-value="67.74" class="pct medium">67.74%</td>
	<td data-value="31" class="abs medium">21/31</td>
	</tr>

<tr>
	<td class="file low" data-value="responseUtils.js"><a href="responseUtils.js.html">responseUtils.js</a></td>
	<td data-value="43.13" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 43%"></div><div class="cover-empty" style="width: 57%"></div></div>
	</td>
	<td data-value="43.13" class="pct low">43.13%</td>
	<td data-value="51" class="abs low">22/51</td>
	<td data-value="30.61" class="pct low">30.61%</td>
	<td data-value="49" class="abs low">15/49</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="9" class="abs medium">5/9</td>
	<td data-value="43.13" class="pct low">43.13%</td>
	<td data-value="51" class="abs low">22/51</td>
	</tr>

<tr>
	<td class="file low" data-value="standardizedErrorUtils.js"><a href="standardizedErrorUtils.js.html">standardizedErrorUtils.js</a></td>
	<td data-value="44.61" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 44%"></div><div class="cover-empty" style="width: 56%"></div></div>
	</td>
	<td data-value="44.61" class="pct low">44.61%</td>
	<td data-value="65" class="abs low">29/65</td>
	<td data-value="51.72" class="pct medium">51.72%</td>
	<td data-value="87" class="abs medium">45/87</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="8" class="abs medium">5/8</td>
	<td data-value="44.61" class="pct low">44.61%</td>
	<td data-value="65" class="abs low">29/65</td>
	</tr>

<tr>
	<td class="file low" data-value="validationUtils.js"><a href="validationUtils.js.html">validationUtils.js</a></td>
	<td data-value="43.67" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 43%"></div><div class="cover-empty" style="width: 57%"></div></div>
	</td>
	<td data-value="43.67" class="pct low">43.67%</td>
	<td data-value="87" class="abs low">38/87</td>
	<td data-value="41.17" class="pct low">41.17%</td>
	<td data-value="85" class="abs low">35/85</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="15" class="abs low">6/15</td>
	<td data-value="43.67" class="pct low">43.67%</td>
	<td data-value="87" class="abs low">38/87</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-16T09:39:32.104Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    