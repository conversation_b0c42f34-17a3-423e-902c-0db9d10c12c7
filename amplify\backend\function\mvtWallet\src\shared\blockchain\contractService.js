const ethers = require('ethers');
const withdrawContractABI = require('./USDCWithdrawal.json');
const { createBlockchainLogger } = require('../utils/logger');

// Standard ERC-20 ABI for USDC contract
const ERC20_ABI = [
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function balanceOf(address account) external view returns (uint256)",
  "function transfer(address to, uint256 amount) external returns (bool)",
  "function transferFrom(address from, address to, uint256 amount) external returns (bool)"
];

// Initialize blockchain connection
let provider;
let wallet;
let withdrawContract;
let usdcContract;

try {
  // Initialize provider
  if (process.env.ETHEREUM_RPC_URL) {
    provider = new ethers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL);
  }

  // Initialize wallet
  if (process.env.PRIVATE_KEY && provider) {
    wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
  }

  // Initialize withdraw contract for USDC operations
  if (process.env.MVT_WITHDRAW_CONTRACT_ADDRESS && wallet) {
    withdrawContract = new ethers.Contract(
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      withdrawContractABI,
      wallet
    );
  }

  // Initialize USDC contract for approval operations
  if (process.env.MVT_USDC_CONTRACT_ADDRESS && wallet) {
    usdcContract = new ethers.Contract(
      process.env.MVT_USDC_CONTRACT_ADDRESS,
      ERC20_ABI,
      wallet
    );
  }
} catch (error) {
  const logger = createBlockchainLogger({}, 'contract_initialization');
  logger.error({
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    operation: 'contract_initialization'
  }, "Failed to initialize blockchain connection");
}

/**
 * Get total USDC liquidity from the withdraw contract with robust error handling
 * @returns {Promise<string>} - Total USDC liquidity in formatted units
 */
async function getContractUSDCBalance() {
  const logger = createBlockchainLogger({}, 'getContractUSDCBalance');

  try {
    logger.info({
      contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      operation: 'getContractUSDCBalance'
    }, "Starting USDC balance check from contract");

    if (!withdrawContract) {
      logger.warn({
        operation: 'getContractUSDCBalance',
        reason: 'contract_not_initialized'
      }, "Withdraw contract not initialized, returning fallback balance");
      return "0"; // Fallback for hybrid architecture
    }

    // First, verify the contract is deployed by checking if it has code
    logger.debug({
      operation: 'getContractUSDCBalance',
      step: 'contract_verification'
    }, "Verifying contract deployment");

    const contractCode = await provider.getCode(process.env.MVT_WITHDRAW_CONTRACT_ADDRESS);
    if (contractCode === "0x") {
      logger.warn({
        operation: 'getContractUSDCBalance',
        contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
        reason: 'contract_not_deployed'
      }, "Contract not deployed at address");
      return "0"; // Fallback for hybrid architecture
    }

    logger.debug({
      operation: 'getContractUSDCBalance',
      codeLength: contractCode.length
    }, "Contract deployment verified");

    // Try to call the contract method with timeout
    logger.debug({
      operation: 'getContractUSDCBalance',
      method: 'getTotalLiquidity'
    }, "Calling getTotalLiquidity method on contract");

    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Contract call timeout")), 10000)
    );

    // Use getTotalLiquidity() method from the USDCWithdrawal.json ABI
    const balancePromise = withdrawContract.getTotalLiquidity();
    const balance = await Promise.race([balancePromise, timeoutPromise]);

    logger.debug({
      operation: 'getContractUSDCBalance',
      rawBalance: balance?.toString()
    }, "Raw total liquidity retrieved from contract");

    // Check if balance is valid
    if (balance === undefined || balance === null) {
      logger.warn({
        operation: 'getContractUSDCBalance',
        reason: 'invalid_balance_response'
      }, "Contract returned undefined/null balance, using fallback");
      return "0";
    }

    // USDC has 6 decimals
    const formattedBalance = ethers.formatUnits(balance, 6);
    logger.info({
      operation: 'getContractUSDCBalance',
      formattedBalance: formattedBalance,
      rawBalance: balance?.toString()
    }, `Formatted USDC balance: ${formattedBalance} USDC`);

    return formattedBalance;
  } catch (error) {
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        code: error.code,
        reason: error.reason,
        stack: error.stack
      },
      operation: 'getContractUSDCBalance'
    }, "Failed to fetch contract USDC balance");

    logger.warn({
      operation: 'getContractUSDCBalance',
      fallbackMode: true
    }, "Using fallback USDC balance for hybrid architecture");

    // For hybrid architecture, we can continue with local operations even if blockchain fails
    // Return a fallback balance instead of throwing an error
    return "0";
  }
}

/**
 * Check and approve USDC spending if needed
 * @param {string} spenderAddress - Address that will spend USDC
 * @param {string} amountInWei - Amount in wei to approve
 * @returns {Promise<object>} - Approval result
 */
async function checkAndApproveUSDC(spenderAddress, amountInWei) {
  try {
    if (!usdcContract || !wallet) {
      console.warn("USDC contract or wallet not initialized:", {
        usdcContract: !!usdcContract,
        wallet: !!wallet,
        usdcTokenAddress: process.env.MVT_USDC_CONTRACT_ADDRESS
      });

      // For development/testing, we can skip approval if USDC contract is not available
      if (!process.env.MVT_USDC_CONTRACT_ADDRESS) {
        console.warn("MVT_USDC_CONTRACT_ADDRESS not set, skipping approval");
        return { success: true, approvalNeeded: false, skipped: true };
      }

      throw new Error("USDC contract or wallet not initialized. Please check MVT_USDC_CONTRACT_ADDRESS environment variable.");
    }

    // Check current allowance
    const currentAllowance = await usdcContract.allowance(wallet.address, spenderAddress);

    // If allowance is sufficient, no need to approve
    if (currentAllowance >= amountInWei) {
      return { success: true, approvalNeeded: false };
    }

    // Approve the spending
    const approveTx = await usdcContract.approve(spenderAddress, amountInWei);

    const approveReceipt = await approveTx.wait();

    return {
      success: true,
      approvalNeeded: true,
      approvalTxHash: approveReceipt.transactionHash,
      approvalGasUsed: approveReceipt.gasUsed.toString()
    };
  } catch (error) {
    console.error("Failed to approve USDC spending:", error);
    throw new Error(`Failed to approve USDC spending: ${error.message}`);
  }
}

/**
 * Deposit USDC to the contract
 * @param {number} amount - Amount of USDC to deposit
 * @returns {Promise<object>} - Transaction result
 */
async function depositUSDCToContract(amount) {
  try {
    if (!withdrawContract) {
      throw new Error("Withdraw contract not initialized");
    }

    // Convert amount to proper decimals (6 decimals for USDC)
    const amountInWei = ethers.parseUnits(amount.toString(), 6);

    // Check and approve USDC spending if needed
    const approvalResult = await checkAndApproveUSDC(
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      amountInWei
    );

    if (!approvalResult.success) {
      throw new Error("Failed to approve USDC spending");
    }

    // Use deposit(amount) method from the USDCWithdrawal.json ABI
    const tx = await withdrawContract.deposit(amountInWei);

    const receipt = await tx.wait();

    return {
      success: true,
      transactionHash: receipt.transactionHash,
      blockNumber: receipt.blockNumber,
      gasUsed: receipt.gasUsed.toString(),
      approvalNeeded: approvalResult.approvalNeeded,
      approvalTxHash: approvalResult.approvalTxHash || null
    };
  } catch (error) {
    console.error("Failed to deposit USDC to contract:", error);

    // Provide user-friendly error messages
    let userFriendlyMessage = "Failed to deposit USDC to the liquidity pool.";

    if (error.message.includes("Insufficient allowance")) {
      userFriendlyMessage = "USDC spending approval failed. Please ensure the admin wallet has sufficient USDC balance and try again.";
    } else if (error.message.includes("insufficient funds")) {
      userFriendlyMessage = "Insufficient USDC balance in admin wallet. Please add USDC to the admin wallet before depositing.";
    } else if (error.message.includes("user rejected")) {
      userFriendlyMessage = "Transaction was rejected. Please approve the transaction to deposit USDC.";
    } else if (error.message.includes("network")) {
      userFriendlyMessage = "Network connection issue. Please check your internet connection and try again.";
    } else if (error.message.includes("gas")) {
      userFriendlyMessage = "Transaction failed due to gas issues. Please try again with higher gas settings.";
    }

    throw new Error(userFriendlyMessage);
  }
}

/**
 * Withdraw USDC from contract to admin wallet
 * @param {number} amount - Amount of USDC to withdraw
 * @returns {Promise<object>} - Transaction result
 */
async function withdrawUSDCFromContract(amount) {
  try {
    if (!withdrawContract) {
      throw new Error("Withdraw contract not initialized");
    }

    // Convert amount to proper decimals (6 decimals for USDC)
    const amountInWei = ethers.parseUnits(amount.toString(), 6);

    // Use withdraw(amount) method from the USDCWithdrawal.json ABI
    const tx = await withdrawContract.withdraw(amountInWei);

    const receipt = await tx.wait();

    return {
      success: true,
      transactionHash: receipt.transactionHash,
      blockNumber: receipt.blockNumber,
      gasUsed: receipt.gasUsed.toString(),
      amount: amount
    };
  } catch (error) {
    console.error("Failed to withdraw USDC from contract:", error);
    throw new Error(`Failed to withdraw USDC: ${error.message}`);
  }
}

/**
 * Transfer USDC from contract to user address
 * @param {string} userAddress - User's wallet address
 * @param {number} amount - Amount of USDC to transfer
 * @returns {Promise<object>} - Transaction result
 */
async function transferUSDCToUser(userAddress, amount) {
  try {
    console.log("Starting USDC transfer:", { userAddress, amount });

    // Validate environment variables
    const envValidation = validateEnvironmentVariables();
    if (!envValidation.isValid) {
      console.error("Environment validation failed:", envValidation);
      throw new Error(`Missing required environment variables: ${envValidation.missing.join(', ')}`);
    }

    // Check blockchain connection first
    if (!isBlockchainConnected()) {
      console.error("Blockchain not connected:", {
        provider: !!provider,
        wallet: !!wallet,
        withdrawContract: !!withdrawContract,
        envValidation
      });
      throw new Error("Blockchain connection not available");
    }

    if (!withdrawContract) {
      throw new Error("Withdraw contract not initialized");
    }

    // Verify contract is deployed
    try {
      const contractCode = await provider.getCode(process.env.MVT_WITHDRAW_CONTRACT_ADDRESS);
      if (contractCode === "0x") {
        throw new Error(`Contract not deployed at address: ${process.env.MVT_WITHDRAW_CONTRACT_ADDRESS}`);
      }
      console.log("Contract deployment verified");
    } catch (codeError) {
      console.error("Failed to verify contract deployment:", codeError);
      throw new Error(`Failed to verify contract deployment: ${codeError.message}`);
    }

    // Convert amount to proper decimals (6 decimals for USDC)
    const amountInWei = ethers.parseUnits(amount.toString(), 6);
    console.log("Converted amount to wei:", amountInWei.toString());

    // Use transferToUser(to, amount) method from the USDCWithdrawal.json ABI
    const logger = createBlockchainLogger({}, 'transferUSDCToUser');
    logger.info({
      operation: 'transferUSDCToUser',
      userAddress: userAddress,
      amount: amount,
      amountInWei: amountInWei.toString()
    }, "Calling contract transferToUser method");

    const tx = await withdrawContract.transferToUser(userAddress, amountInWei);
    console.log("Transaction submitted:", { hash: tx.hash });

    console.log("Waiting for transaction receipt...");
    const receipt = await tx.wait();
    console.log("Transaction receipt received:", {
      transactionHash: receipt.transactionHash,
      hash: receipt.hash,
      blockNumber: receipt.blockNumber,
      gasUsed: receipt.gasUsed?.toString(),
      status: receipt.status
    });

    // Get the transaction hash - it can be in either transactionHash or hash property
    const txHash = receipt.transactionHash || receipt.hash;

    // Validate receipt and transaction hash
    if (!receipt || !txHash) {
      console.error("Invalid receipt received:", receipt);
      throw new Error("Transaction receipt is invalid or missing transaction hash");
    }

    // Validate transaction was successful
    if (receipt.status !== 1) {
      console.error("Transaction failed with status:", receipt.status);
      throw new Error(`Transaction failed with status: ${receipt.status}`);
    }

    console.log("✅ USDC transfer successful:", {
      transactionHash: txHash,
      userAddress,
      amount,
      blockNumber: receipt.blockNumber
    });

    return {
      success: true,
      transactionHash: txHash,
      blockNumber: receipt.blockNumber,
      gasUsed: receipt.gasUsed?.toString() || "0",
      to: userAddress,
      amount: amount
    };
  } catch (error) {
    console.error("Failed to transfer USDC to user:", error);
    console.error("Error details:", {
      message: error.message,
      code: error.code,
      reason: error.reason,
      stack: error.stack
    });
    throw new Error(`Failed to transfer USDC: ${error.message}`);
  }
}

/**
 * Validate environment variables required for blockchain operations
 * @returns {object} - Validation result with details
 */
function validateEnvironmentVariables() {
  const requiredVars = {
    ETHEREUM_RPC_URL: process.env.ETHEREUM_RPC_URL,
    PRIVATE_KEY: process.env.PRIVATE_KEY,
    MVT_WITHDRAW_CONTRACT_ADDRESS: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
    MVT_USDC_CONTRACT_ADDRESS: process.env.MVT_USDC_CONTRACT_ADDRESS
  };

  const missing = [];
  const present = {};

  for (const [key, value] of Object.entries(requiredVars)) {
    if (!value) {
      missing.push(key);
    } else {
      present[key] = value.length > 10 ? `${value.substring(0, 10)}...` : value;
    }
  }

  return {
    isValid: missing.length === 0,
    missing,
    present,
    details: {
      totalRequired: Object.keys(requiredVars).length,
      totalPresent: Object.keys(present).length,
      totalMissing: missing.length
    }
  };
}

/**
 * Check if blockchain connection is available
 * @returns {boolean} - Connection status
 */
function isBlockchainConnected() {
  const envValidation = validateEnvironmentVariables();
  const basicConnection = !!(provider && wallet && withdrawContract);

  if (!basicConnection || !envValidation.isValid) {
    console.warn("Blockchain connection not available:", {
      basicConnection,
      provider: !!provider,
      wallet: !!wallet,
      withdrawContract: !!withdrawContract,
      usdcContract: !!usdcContract,
      environmentValidation: envValidation
    });
  }

  return basicConnection && envValidation.isValid;
}

/**
 * Get wallet address
 * @returns {string|null} - Wallet address or null if not initialized
 */
function getWalletAddress() {
  return wallet ? wallet.address : null;
}

/**
 * Test contract connection and basic functionality
 * @returns {Promise<object>} - Test result
 */
async function testContractConnection() {
  try {
    const envValidation = validateEnvironmentVariables();
    const isConnected = isBlockchainConnected();

    const result = {
      environmentVariables: envValidation,
      basicConnection: isConnected,
      contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      walletAddress: getWalletAddress(),
      tests: {}
    };

    if (!isConnected) {
      result.tests.connectionTest = { success: false, error: "Basic connection failed" };
      return result;
    }

    // Test contract deployment
    try {
      const contractCode = await provider.getCode(process.env.MVT_WITHDRAW_CONTRACT_ADDRESS);
      result.tests.deploymentTest = {
        success: contractCode !== "0x",
        contractCode: contractCode.length > 10 ? `${contractCode.substring(0, 10)}...` : contractCode
      };
    } catch (error) {
      result.tests.deploymentTest = { success: false, error: error.message };
    }

    // Test contract balance call
    try {
      const balance = await getContractUSDCBalance();
      result.tests.balanceTest = { success: true, balance };
    } catch (error) {
      result.tests.balanceTest = { success: false, error: error.message };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      environmentVariables: validateEnvironmentVariables(),
      basicConnection: false
    };
  }
}

// HYBRID ARCHITECTURE: This service handles USDC blockchain operations only
// MVT operations are handled locally in the wallet service
// Exchange rates are calculated locally in the exchange rate service
module.exports = {
  getContractUSDCBalance,
  checkAndApproveUSDC,
  depositUSDCToContract,
  transferUSDCToUser,
  withdrawUSDCFromContract,
  isBlockchainConnected,
  getWalletAddress,
  validateEnvironmentVariables,
  testContractConnection
};
